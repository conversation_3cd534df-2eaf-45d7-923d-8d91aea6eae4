package com.caict.nsm.project.api.web.controller.nsm;

import com.alibaba.fastjson.JSONObject;
import com.caict.nsm.project.api.web.service.nsm.NsmIdcDataEsWebService;
import com.caict.nsm.project.api.web.service.security.AuthWebService;
import com.caict.nsm.project.system.model.dto.nsm.NsmIdcDataEsDTO;
import com.caict.nsm.project.system.model.vo.nsm.*;
import com.caict.nsm.project.system.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.List;

/**
 * @Title: NsmSecurityRiskInfoController
 * <AUTHOR>
 * @Package com.caict.nsm.project.api.web.controller.nsm
 * @Date 2024/9/12 17:16
 * @description:
 */

@RestController
@RequestMapping(value = "/apiWeb/nsm/nsmIdcDataEs")
@Api(value = "web端-业务-Idc数据表Es表分析结果(输入)接口", tags = "web端-业务-Idc数据表Es表分析结果(输出)接口")
public class NsmIdcDataEsController {

    private static final Logger LOG = LoggerFactory.getLogger(NsmIdcDataEsController.class);

    @Autowired
    private NsmIdcDataEsWebService esWebService;

    @Autowired
    private AuthWebService authWebService;


    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nsmIdcDataEsVO", value = "idc数据表对象", required = true, paramType = "body", dataType = "nsmIdcDataEsVO"),
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "header", dataType = "String")
    })
    @PostMapping(value = "/findAllPage")
    public JSONObject findAllPage(@RequestBody NsmIdcDataEsVO nsmIdcDataEsVO, @RequestHeader("token") String token){
        PageInfo<NsmIdcDataEsDTO> pageInfo = esWebService.findAllPage(nsmIdcDataEsVO,token);
        if (pageInfo != null) return JSONResult.getSuccessJson(pageInfo,"查询成功");
        return JSONResult.getFailureJson("查询失败");
    }

    @ApiOperation(value = "根据企业安全风险分析id查询",notes = "根据id查询接口")
    @PostMapping(value = "findOneDetail")
    public JSONObject findOneDetail(@RequestBody NsmIdcDataDetailVO nsmIdcDataDetailVO, @RequestHeader("token") String token){
        return JSONResult.getSuccessJson(esWebService.findOneDetail(nsmIdcDataDetailVO),"查询成功");
    }

    @ApiOperation(value = "条件导出风险数据",notes = "条件导出风险数据接口")
    @PostMapping(value = "exportRiskData")
    public JSONObject exportRiskData(@RequestBody NsmIdcDataEsVO nsmIdcDataEsVO, @RequestHeader("token") String token){
        return JSONResult.getSuccessJson(esWebService.exportRiskData(nsmIdcDataEsVO),"查询成功");
    }

    @ApiOperation(value = "修改idc导入风险数据",notes = "修改idc导入风险数据")
    @PostMapping(value = "updateRiskData")
    public JSONObject updateRiskData(@RequestBody NsmIdcDataEsVO nsmIdcDataEsVO, @RequestHeader("token") String token){
        return JSONResult.getSuccessJson(esWebService.updateRiskData(nsmIdcDataEsVO),"操作成功");
    }


    @ApiOperation(value = "删除idc导入风险数据",notes = "删除idc导入风险数据")
    @GetMapping(value = "deleteRiskData/{id}")
    public JSONObject deleteRiskData(@PathVariable("id") String id, @RequestHeader("token") String token){
        return JSONResult.getSuccessJson(esWebService.deleteRiskData(id),"操作成功");
    }




    @ApiOperation(value = "获取指定时间段内的企业风险数据", notes = "获取指定时间段内的企业风险数据接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "查询的开始日期", required = true, paramType = "path", dataType = "string"),
            @ApiImplicitParam(name = "endDate", value = "查询的结束日期", required = true, paramType = "path", dataType = "string"),
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "path", dataType = "int"),
            @ApiImplicitParam(name = "rows", value = "每页的条数", required = true, paramType = "path", dataType = "int")
    })
    @GetMapping("/findDataByMonth/{startDate}/{endDate}/{page}/{rows}")
    public JSONObject findDataByMonth(
            @PathVariable String startDate,
            @PathVariable String endDate,
            @PathVariable int page,
            @PathVariable int rows,
            @RequestHeader("token") String token,
            HttpServletRequest request) {

        // 获取客户端 IP 地址
        String ip = request.getRemoteAddr();

        // 获取当前时间
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 记录请求的详细日志
        LOG.info("【请求日志】收到请求: 时间: {}, IP: {}, 查询时间段: {} 到 {}, 页码: {}, 每页条数: {}, 请求头token: {}",
                currentTime, ip, startDate, endDate, page, rows, token);

        // 处理业务逻辑
        PageInfo<NsmIdcDataEsDTO> dataList = esWebService.findDataByMonth(startDate, endDate, page, rows, token);

        // 打印返回的数据或错误日志
        if (dataList != null && !dataList.getList().isEmpty()) {
            // 记录成功响应日志
            LOG.info("【响应日志】查询成功: 时间: {}, 返回结果数量: {}", currentTime, dataList.getList().size());
            return JSONResult.getSuccessJson(dataList, "查询成功");
        } else {
            // 记录失败响应日志
            LOG.warn("【响应日志】查询失败: 时间: {}, 无数据返回", currentTime);
            return JSONResult.getFailureJson("查询失败, 无数据返回");
        }
    }

    @ApiOperation(value = "导出指定时间段内风险数据",notes = "导出指定时间段内风险数据接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "查询的开始日期", required = true, paramType = "path", dataType = "string"),
            @ApiImplicitParam(name = "endDate", value = "查询的结束日期", required = true, paramType = "path", dataType = "string")
    })
    @GetMapping("/exportDataByMonth/{startDate}/{endDate}")
    public JSONObject exportRiskDataByDate(
            @PathVariable String startDate,
            @PathVariable String endDate,
            @RequestHeader("token") String token){
            return JSONResult.getSuccessJson(esWebService.exportRiskDataByDate(startDate, endDate,token),"操作成功");


    }

    @ApiOperation(value = "删除指定时间段内导入的ES数据", notes = "根据importDate时间段删除ES数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始日期(格式:yyyy-MM-dd)", required = true, paramType = "path", dataType = "string"),
            @ApiImplicitParam(name = "endDate", value = "结束日期(格式:yyyy-MM-dd)", required = true, paramType = "path", dataType = "string"),
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "header", dataType = "String")
    })
    @DeleteMapping("/deleteDataByDateRange/{startDate}/{endDate}")
    public JSONObject deleteDataByDateRange(
            @PathVariable String startDate,
            @PathVariable String endDate,
            @RequestHeader("token") String token) {

        // 获取当前时间
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 记录请求的详细日志
        LOG.info("【请求日志】收到删除请求: 时间: {}, 删除时间段: {} 到 {}", currentTime, startDate, endDate);

        // 调用服务层方法执行删除操作
        return JSONResult.getSuccessJson(esWebService.deleteDataByDateRange(startDate, endDate, token), "操作成功");
    }
}
