package com.caict.nsm.project.api.web.service.nsm;


import com.alibaba.fastjson.JSONObject;
import com.caict.nsm.project.api.web.utils.EncryptionUtil;
import com.caict.nsm.project.domain.business.mapper.es.NsmIdcDataEsMapper;
import com.caict.nsm.project.domain.business.service.business.NsmIdcDataService;
import com.caict.nsm.project.system.data.service.ElasticsearchBasicService;
import com.caict.nsm.project.system.model.dto.nsm.NsmIdcDataDTO;
import com.caict.nsm.project.system.model.dto.nsm.NsmIdcDataEsDTO;
import com.caict.nsm.project.system.model.entity.nsm.NsmIdcDataEs;
import com.caict.nsm.project.system.model.vo.nsm.NsmIdcDataDetailVO;
import com.caict.nsm.project.system.model.vo.nsm.NsmIdcDataEsVO;
import com.caict.nsm.project.system.utils.util.JSONResult;
import com.caict.nsm.project.system.utils.util.MyDate;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: NsmIdcDataWebService
 * <AUTHOR>
 * @Package com.caict.nsm.project.api.web.service.nsm
 * @Date 2024/10/14 17:14
 * @description: idc表数据
 */
@Service
public class NsmIdcDataEsWebService extends ElasticsearchBasicService<NsmIdcDataEs, NsmIdcDataEsVO> {

    private static final Logger LOG = LoggerFactory.getLogger(NsmIdcDataEsWebService.class);

    @Autowired
    private NsmIdcDataService service;
    @Autowired
    private NsmIdcDataEsMapper nsmIdcDataEsMapper;
    // 文件下载地址
    @Value("${caict.myFilePath}")
    private String myFilePath;

    // 在 readCsv 中添加 SecretKey
    SecretKey secretKey = EncryptionUtil.generateKey();

    public NsmIdcDataEsWebService() throws Exception {
    }

    /**
     * 批量添加
     * */
    public void saveBatch(List<NsmIdcDataEs> list){
        if (list != null){
            int size = list.size();

            LOG.info("开启数据elastic操作，数量：" + size);
            if (size > 50000) {
                for (var i = 0; i < size; i += 50000){
                    List<NsmIdcDataEs> collect = list.stream().skip(i).limit(50000).collect(Collectors.toList());
                    nsmIdcDataEsMapper.saveAll(collect);
                }
            }else {
                nsmIdcDataEsMapper.saveAll(list);
            }
            LOG.info("数据elastic操作结束");
        }
    }

    /**
     * 分页查询
     * @param vo 查询条件
     * @param token 认证令牌
     * @return 分页结果
     */
    public PageInfo<NsmIdcDataEsDTO> findAllPage(NsmIdcDataEsVO vo, String token) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        // 使用 should 和 must 来构建查询条件
        if (StringUtils.isNoneBlank(vo.getEnterpriseName())) {
            boolQueryBuilder.should(QueryBuilders.wildcardQuery("enterprise_name", "*" + vo.getEnterpriseName() + "*"));
        }
        if (StringUtils.isNoneBlank(vo.getEnterpriseType())) {
            boolQueryBuilder.should(QueryBuilders.wildcardQuery("enterprise_type", "*" + vo.getEnterpriseType() + "*"));
        }
        if (StringUtils.isNoneBlank(vo.getEnterpriseBelongPlace())) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("enterprise_belong_place", vo.getEnterpriseBelongPlace()));
        }
        if (StringUtils.isNoneBlank(vo.getAttackBelongCountry())) {
            boolQueryBuilder.should(QueryBuilders.wildcardQuery("attack_belong_country", "*" + vo.getAttackBelongCountry() + "*"));
        }
        if (StringUtils.isNoneBlank(vo.getAttackRiskType())) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("attack_risk_type", vo.getAttackRiskType()));
        }
        if (StringUtils.isNoneBlank(vo.getAttackRiskLevel())) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("attack_risk_level", vo.getAttackRiskLevel()));
        }

        // 使用期限
        if (vo.getStartTime() != null) {
            String startFormat = new SimpleDateFormat("yyyy年MM月dd日").format(vo.getStartTime());
            boolQueryBuilder.must(QueryBuilders.matchQuery("attack_date", startFormat));
        }
        if (vo.getEndTime() != null) {
            String endFormat = new SimpleDateFormat("yyyy年MM月dd日").format(vo.getEndTime());
            boolQueryBuilder.must(QueryBuilders.matchQuery("attack_date", endFormat));
        }

        // 执行查询并返回结果
        var esPage = this.searchHitsPage(vo, boolQueryBuilder);
        return pageToDto(esPage);
    }

    //分页结果对象转换
    protected PageInfo<NsmIdcDataEsDTO> pageToDto(PageInfo<NsmIdcDataEs> esPage){
        var esList = esPage.getList();
        var list = esToDtoList(esList);

        PageInfo<NsmIdcDataEsDTO> page = new PageInfo<>();
        BeanUtils.copyProperties(esPage,page);

        page.setList(list);

        return page;
    }

    //对象集合转换
    protected List<NsmIdcDataEsDTO> esToDtoList(List<NsmIdcDataEs> esList){
        if (esList != null) {
            var list = new ArrayList<NsmIdcDataEsDTO>();
            esList.forEach(es -> {
                var dto = esToDto(es);
                if (dto != null) list.add(dto);
            });
            return list;
        }
        return null;
    }

    //对象转换
    protected NsmIdcDataEsDTO esToDto(NsmIdcDataEs es) {
        if (es != null) {
            var nsmIdcDataEsDTO = new NsmIdcDataEsDTO();
            BeanUtils.copyProperties(es, nsmIdcDataEsDTO);

            if (es.getId() != null) {
                nsmIdcDataEsDTO.setId(String.valueOf(es.getId()));
            }

            if (es.getPayload() != null) {
                // 解码攻击载荷
                try {
                    // 使用 Base64 解码
                    byte[] decodedBytes = Base64.getDecoder().decode(es.getPayload());
                    String decodedPayload = new String(decodedBytes, "UTF-8");

                    // 过滤掉不可打印字符
                    StringBuilder filteredPayload = new StringBuilder();
                    for (char c : decodedPayload.toCharArray()) {
                        if (isPrintable(c)) {
                            filteredPayload.append(c);
                        }
                    }

                    // 设置解码后的 payload
                    nsmIdcDataEsDTO.setPayload(filteredPayload.toString());
                } catch (IllegalArgumentException | UnsupportedEncodingException e) {
                    // 处理解码错误
                    LOG.warn("Base64 解码失败，payload: " + es.getPayload());
                    nsmIdcDataEsDTO.setPayload(""); // 或设置为其他默认值
                }
            }

            return nsmIdcDataEsDTO;
        }
        return null;
    }

    // 检查字符是否为可打印字符
    private boolean isPrintable(char c) {
        return c >= 32 && c <= 126; // ASCII 可打印字符范围
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public NsmIdcDataEsDTO findOne(String id) {
        NsmIdcDataEsDTO nsmIdcDataEsDTO = new NsmIdcDataEsDTO();
        var nsmSecurityRiskInfo = service.findById(Long.valueOf(id));
        if (nsmSecurityRiskInfo != null) {
            BeanUtils.copyProperties(nsmSecurityRiskInfo, nsmIdcDataEsDTO);
            return nsmIdcDataEsDTO;
        }
        return null;

    }


    public JSONObject findOneDetail(NsmIdcDataDetailVO nsmIdcDataDetailVO){
        String importDate = nsmIdcDataDetailVO.getImportDate().replace("-","");
        return JSONResult.getSuccessJson(service.findByIdImportDate(nsmIdcDataDetailVO.getId(),importDate));
    }

    /**
     * 条件导出风险数据
     * @param nsmIdcDataRiskInfoEsVO
     * @return
     */

    public JSONObject exportRiskData(NsmIdcDataEsVO nsmIdcDataRiskInfoEsVO) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        // 导入期限
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        if (nsmIdcDataRiskInfoEsVO.getStartTime() != null) {
            String format = simpleDateFormat.format(nsmIdcDataRiskInfoEsVO.getStartTime());
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_date").gte(format));
        }
        if (nsmIdcDataRiskInfoEsVO.getEndTime() != null) {
            String format = simpleDateFormat.format(nsmIdcDataRiskInfoEsVO.getEndTime());
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_date").lte(format));
        }
        int count = (int) this.count(boolQueryBuilder);
        //设置每页数量为50000
        int rows = 1000;
        if (count > 0) {
            List<NsmIdcDataEs> list = new ArrayList<>();
            int pageNum = count % rows == 0 ? count / rows : count / rows + 1;
            nsmIdcDataRiskInfoEsVO.setRows(rows);
            for (int i = 1; i <= pageNum; i++) {
                nsmIdcDataRiskInfoEsVO.setPage(i);
                var dataList = this.searchHitsPage(nsmIdcDataRiskInfoEsVO, boolQueryBuilder);
                list.addAll(dataList.getList());
            }
            // 生成excel文件
            String export = "export/";
            String fileName = new Date().getTime() + "_风险信息.xlsx";
            return JSONResult.getSuccessJson(createExcel(list, fileName, export));
        }

        return JSONResult.getSuccessJson("导出数据为空！");
    }

    private String createExcel(List<NsmIdcDataEs> dataList, String fileName, String export) {
        try {
            //判断路径
            File uploadDir = new File(myFilePath + export);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            SXSSFWorkbook wb = new SXSSFWorkbook();
            SXSSFSheet sheet = wb.createSheet(fileName);
            //自适应列宽
            sheet.trackAllColumnsForAutoSizing();
            SXSSFRow row;
            createFirstRow(sheet);
            for (int i = 0; i < dataList.size(); i++) {
                NsmIdcDataEs es = dataList.get(i);
//                sheet.autoSizeColumn(i + 1);
                row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(es.getEnterpriseName());
//                row.createCell(1).setCellValue(es.getEnterpriseIp());
                row.createCell(1).setCellValue(es.getEnterpriseBelongPlace());
                row.createCell(2).setCellValue(es.getAttackIp());
                row.createCell(3).setCellValue(es.getAttackPort());
                row.createCell(4).setCellValue(es.getAttackBelongCountry());
//                row.createCell(6).setCellValue(es.getAttackBelongCountry());
                row.createCell(5).setCellValue(String.valueOf(es.getAttackDate()));
//                row.createCell(6).setCellValue(es.getAttackTime());
                row.createCell(6).setCellValue(es.getAttackRiskLevel());
                row.createCell(7).setCellValue(es.getAttackRiskType());
                row.createCell(8).setCellValue(es.getAttackCount());
                LocalDate importDate = es.getImportDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy"); // 自定义格式
                if (importDate != null){
                    row.createCell(9).setCellValue(importDate.format(formatter));
                }
                row.createCell(10).setCellValue(es.getPayload());
                row.createCell(11).setCellValue(es.getProtocolType());
                row.createCell(12).setCellValue(es.getProtocolInfo());

            }

            FileOutputStream fileOutputStream = new FileOutputStream(myFilePath + export + File.separator + fileName);
            wb.write(fileOutputStream);
            fileOutputStream.close();
            wb.dispose();
            return export + fileName;
        }catch (IOException e){
            e.printStackTrace();
        }
        return null;
    }

    private void createFirstRow(SXSSFSheet sheet) {
        SXSSFRow firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("企业名称");
//        firstRow.createCell(1).setCellValue("企业 IP");
//        firstRow.createCell(2).setCellValue("企业 IP 端口号"); // 这里保持一致性
        firstRow.createCell(1).setCellValue("企业归属地");
        firstRow.createCell(2).setCellValue("攻击 IP");
        firstRow.createCell(3).setCellValue("攻击 IP 端口号"); // 假设这个字段存在
        firstRow.createCell(4).setCellValue("攻击 IP 归属地国家");
        firstRow.createCell(5).setCellValue("攻击日期"); // 更新为攻击日期
        firstRow.createCell(6).setCellValue("风险等级");
        firstRow.createCell(7).setCellValue("风险类型");
        firstRow.createCell(8).setCellValue("累计次数"); // 假设这个字段存在
        firstRow.createCell(9).setCellValue("导入时间"); // 导入时间
        firstRow.createCell(10).setCellValue("攻击载荷");
        firstRow.createCell(11).setCellValue("协议类型"); // 导入时间
        firstRow.createCell(12).setCellValue("协议信息");
    }

    public JSONObject updateRiskData(NsmIdcDataEsVO nsmIdcDataEsVO){
        List<NsmIdcDataEs> list = new ArrayList<>();
        NsmIdcDataEs nsmIdcDataEs = new NsmIdcDataEs();
        BeanUtils.copyProperties(nsmIdcDataEsVO,nsmIdcDataEs);
        saveBatch(list);
        return JSONResult.getSuccessJson("操作成功！");
    }

    public JSONObject deleteRiskData(String id){
        nsmIdcDataEsMapper.deleteById(Long.valueOf(id));
        return JSONResult.getSuccessJson(service.delete(Long.valueOf(id)));
    }

    /**
     * 获取指定时间段内的企业风险数据 查询月度数据
     * @param startDate
     * @param endDate
     * @return
     */
    public PageInfo<NsmIdcDataEsDTO> findDataByMonth(String startDate, String endDate,int page, int rows,String token) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        NsmIdcDataEsVO vo  = new NsmIdcDataEsVO();
        vo.setPage(page);
        vo.setRows(rows);
        // 导入期限
        if (!startDate.isEmpty() && !endDate.isEmpty()) {
            startDate = startDate.replace("-","");
            endDate = endDate.replace("-","");
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_date").gte(startDate).lte(endDate));
        }
        var esPage = this.searchHitsPage(vo,boolQueryBuilder);
        return pageToDto(esPage);
    }

    public JSONObject exportMonthReport(String month) throws IOException, InvalidFormatException {
        //查出对应月份的风险数据
        List<NsmIdcDataDTO> dataByMonth = service.findDataByMonth(month);
        //按类型进行分组
        Map<String, List<NsmIdcDataDTO>> map = dataByMonth.stream().collect(Collectors.groupingBy(NsmIdcDataDTO::getAttackRiskType));



        return JSONResult.getSuccessJson("月报生成成功！");
    }

    public JSONObject exportRiskDataByDate(String startDate, String endDate, String token) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        // 处理开始日期
        if (startDate != null && !startDate.isEmpty()) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_date").gte(startDate.replace("-", "")));
        }

        // 处理结束日期
        if (endDate != null && !endDate.isEmpty()) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attack_date").lte(endDate.replace("-", "")));
        }

        int count = (int) this.count(boolQueryBuilder);
        // 设置每页数量为50000
        int rows = 1000;

        if (count > 0) {
            List<NsmIdcDataEs> list = new ArrayList<>();
            int pageNum = count % rows == 0 ? count / rows : count / rows + 1;

            for (int i = 1; i <= pageNum; i++) {
                // 创建新的 NsmIdcDataEsVO 对象
                NsmIdcDataEsVO nsmIdcDataRiskInfoEsVO = new NsmIdcDataEsVO();
                nsmIdcDataRiskInfoEsVO.setPage(i);
                nsmIdcDataRiskInfoEsVO.setRows(rows);

                // 执行查询
                var dataList = this.searchHitsPage(nsmIdcDataRiskInfoEsVO, boolQueryBuilder);
                list.addAll(dataList.getList());
            }

            // 生成 Excel 文件
            String export = "export/";
            String fileName = new Date().getTime() + "_风险信息.xlsx";
            return JSONResult.getSuccessJson(createExcel(list, fileName, export));
        }

        return JSONResult.getSuccessJson("导出数据为空！");
    }

    /**
     * 删除指定时间段内导入的ES数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param token 认证令牌
     * @return 删除结果
     */
    public JSONObject deleteDataByDateRange(String startDate, String endDate, String token) {
        try {
            LOG.info("开始删除时间段内的数据: {} 到 {}", startDate, endDate);
            
            // 转换日期格式
            if (startDate == null || startDate.isEmpty() || endDate == null || endDate.isEmpty()) {
                return JSONResult.getFailureJson("开始日期和结束日期不能为空");
            }
            
            // 格式化日期
            String formattedStartDate = startDate.replace("-", "");
            String formattedEndDate = endDate.replace("-", "");
            
            // 使用原生的Elasticsearch客户端执行删除查询
            // 这里假设您的项目中已经注入了RestHighLevelClient
            @Autowired
            private RestHighLevelClient client;
            
            // 构建删除查询
            DeleteByQueryRequest request = new DeleteByQueryRequest("nsm_idc_data_es");
            
            // 设置查询条件
            QueryBuilder queryBuilder = QueryBuilders.rangeQuery("import_date")
                    .gte(formattedStartDate)
                    .lte(formattedEndDate);
            request.setQuery(queryBuilder);
            
            // 设置批量大小
            request.setBatchSize(1000);
            request.setRefresh(true);
            
            // 执行删除
            BulkByScrollResponse response = client.deleteByQuery(request, RequestOptions.DEFAULT);
            long deletedCount = response.getDeleted();
            
            LOG.info("成功删除数据: {} 条", deletedCount);
            return JSONResult.getSuccessJson("成功删除 " + deletedCount + " 条数据");
            
        } catch (Exception e) {
            LOG.error("删除数据时发生错误: ", e);
            return JSONResult.getFailureJson("删除数据时发生错误: " + e.getMessage());
        }
    }

}

