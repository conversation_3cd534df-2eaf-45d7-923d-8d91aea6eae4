package com.caict.nsm.project.system.model.vo.nsm;

import com.caict.nsm.project.system.model.entity.nsm.NsmSecurityRiskInfo;
import com.caict.nsm.project.system.model.vo.security.FileVO;
import com.caict.nsm.project.system.utils.annotation.TableFieId;
import com.caict.nsm.project.system.utils.annotation.TableId;
import com.caict.nsm.project.system.utils.annotation.TablePageNum;
import com.caict.nsm.project.system.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Title: NsmSecurityRiskInfoVO
 * <AUTHOR>
 * @Package com.caict.nsm.project.system.model.vo.nsm
 * @Date 2024/9/13 11:41
 * @description:
 */
@lombok.Data
@ApiModel(value = "nsmSecurityRiskInfoVO", description = "企业风险VO对象")
public class NsmSecurityRiskInfoVO {

    @TablePageNum
    private int page;

    @TablePageSize
    private int rows;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "数据 ID")
    private String dataId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "企业IP")
    private String enterpriseIp;

    @ApiModelProperty(value = "企业端口")
    private String enterprisePort;

    @ApiModelProperty(value = "企业归属地")
    private String enterpriseBelongPlace;

    @ApiModelProperty(value = "攻击IP")
    private String attackIp;

    @ApiModelProperty(value = "攻击端口")
    private String attackPort;

    @ApiModelProperty(value = "攻击者发起攻击的载体信息")
    private String attackPayload;

    @ApiModelProperty(value = "风险等级")
    private String attackRiskLevel;

    @ApiModelProperty(value = "风险类型")
    private String attackRiskType;

    @ApiModelProperty(value = "风险事件开始时间和结束时间，精确到秒")
    private String riskEvenTime;

    @ApiModelProperty(value = "风险事件发生次数")
    private Integer riskEventCount;

    private List<String> fileIds;

}
