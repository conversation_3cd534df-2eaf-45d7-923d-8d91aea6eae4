package com.caict.nsm.project.domain.business.mapper.es;


import com.caict.nsm.project.system.model.entity.nsm.NsmIdcDataEs;

import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Title: NsmIdcDataMapper
 * <AUTHOR>
 * @Package com.caict.nsm.project.domain.business.mapper.business
 * @Date 2024/10/14 17:20
 * @description: idc表数据Mapper
 */

@Repository
public interface NsmIdcDataEsMapper extends ElasticsearchRepository<NsmIdcDataEs,Long> {

    /**
     * 批量删除数据
     * @param ids ID列表
     */
    void deleteAllById(List<Long> ids);
}
