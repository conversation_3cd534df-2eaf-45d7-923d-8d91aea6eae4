package com.caict.nsm.project.domain.business.mapper.business;

import com.caict.nsm.project.system.data.mapper.BasicMapper;
import com.caict.nsm.project.system.model.dto.nsm.NsmSampleResultInfoDTO;
import com.caict.nsm.project.system.model.dto.nsm.NsmTaskInfoDTO;
import com.caict.nsm.project.system.model.entity.nsm.NsmTaskInfo;
import com.caict.nsm.project.system.model.vo.nsm.NsmTaskInfoVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/19 9:58
 * @description:
 */
@Repository
public interface NsmTaskInfoMapper extends BasicMapper<NsmTaskInfo> {

    @Select("SELECT * FROM nsm_task_info ")
    List<NsmTaskInfoDTO> findByPage(@Param("nsmTaskInfoVO") NsmTaskInfoVO nsmTaskInfoVO);

    @Select("SELECT * FROM nsm_task_info where id=#{id}")
    NsmTaskInfo findBytaskId(@Param("id") Long id);

}
