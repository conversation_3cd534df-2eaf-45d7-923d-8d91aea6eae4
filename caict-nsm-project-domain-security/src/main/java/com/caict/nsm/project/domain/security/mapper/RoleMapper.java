package com.caict.nsm.project.domain.security.mapper;

import com.caict.nsm.project.system.data.mapper.BasicMapper;
import com.caict.nsm.project.system.model.dto.security.RoleDTO;
import com.caict.nsm.project.system.model.entity.security.Role;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2019-10-24.
 */
@Repository
public interface RoleMapper extends BasicMapper<Role> {

    @Select("select sys_role.* from sys_role,sys_role_users where sys_role.id = sys_role_users.role_id and sys_role_users.users_id = #{usersId}")
    RoleDTO findRoleByUsersId(@Param("usersId") long usersId);

    @Select("select * from sys_role where id != '1'")
    @Results({
            @Result(property = "usersList",column = "ID",many = @Many(select = "com.caict.nsm.project.domain.security.mapper.UsersMapper.findUsersByRoleId"))
    })
    List<RoleDTO> findAllRoleDTO();

    @Select("select * from sys_role where id = #{roleId}")
    @Results({
            @Result(property = "id",column = "ID"),
            @Result(property = "usersDTOs",column = "ID",many = @Many(select = "com.caict.nsm.project.domain.security.mapper.UsersMapper.findAllUsersByRoleId")),
            @Result(property = "menuDTOs",column = "ID",many = @Many(select = "com.caict.nsm.project.domain.security.mapper.MenuMapper.findAllMenuByRoleId"))
    })
    RoleDTO findOneRoleDTO(@Param("roleId") long roleId);

    @Select("select * from sys_role where id != '1' order by sort ")
    List<RoleDTO> findAllDto();

    /**
     * 根据类型查询
     * */
    @Select("select * from sys_role where type = #{type}")
    List<RoleDTO> findAllByType(@Param("type") String type);

    /**
     * 根据名称查询数量
     * */
    @Select("select count(1) from sys_role where name = #{name}")
    int selectCountByName(@Param("name") String name);
}
